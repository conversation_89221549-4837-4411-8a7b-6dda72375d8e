package com.glowxq.wingman.article.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.wingman.article.pojo.po.Article;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Article修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Schema(description = "Article修改DTO")
public class ArticleUpdateDTO implements BaseDTO {

    @Schema(description = "商品ID")
    private Long id;

    @Schema(description = "案例用户")
    private Long userId;

    @Schema(description = "案例专家")
    private Long expertUserId;

    @Schema(description = "成果摘要")
    private String achievement;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "案例人")
    private String articleUser;

    @Schema(description = "案例标题")
    private String title;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "富文本介绍")
    private String content;

    @Schema(description = "案例主图")
    private String image;

    @Schema(description = "案例图集")
    private String imageGallery;

    @Schema(description = "专家联系方式")
    private String expertContact;

    @Schema(description = "专家二维码")
    private String expertQrCode;

    @Schema(description = "状态")
    private Boolean enable;

    @Schema(description = "标签id")
    private List<Long> tagIds;

    @Override
    public Article buildEntity() {
        return BeanCopyUtils.copy(this, Article.class);
    }
}