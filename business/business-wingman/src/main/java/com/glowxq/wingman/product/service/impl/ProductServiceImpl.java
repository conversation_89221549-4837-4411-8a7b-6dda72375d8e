package com.glowxq.wingman.product.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.system.meta.pojo.po.MetaTag;
import com.glowxq.system.meta.service.MetaTagService;
import com.glowxq.wingman.common.enums.WingmanTagBind;
import com.glowxq.wingman.product.mapper.ProductMapper;
import com.glowxq.wingman.product.pojo.dto.ProductCreateDTO;
import com.glowxq.wingman.product.pojo.dto.ProductImportDTO;
import com.glowxq.wingman.product.pojo.dto.ProductListDTO;
import com.glowxq.wingman.product.pojo.dto.ProductUpdateDTO;
import com.glowxq.wingman.product.pojo.po.Product;
import com.glowxq.wingman.product.pojo.vo.ProductVO;
import com.glowxq.wingman.product.service.ProductService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.util.List;

/**
 * <p>
 * 商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Service
@RequiredArgsConstructor
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    private final MetaTagService metaTagService;

    private static QueryWrapper buildQueryWrapper(ProductListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(Product.class);
        wrapper.eq(Product::getCategoryId, dto.getCategoryId(), Utils.isNotNull(dto.getCategoryId()));
        wrapper.like(Product::getName, dto.getName(), Utils.isNotNull(dto.getName()));
        wrapper.eq(Product::getPrice, dto.getPrice(), Utils.isNotNull(dto.getPrice()));
        wrapper.eq(Product::getContent, dto.getContent(), Utils.isNotNull(dto.getContent()));
        // wrapper.eq(Product::getType, dto.getType(), Utils.isNotNull(dto.getType()));
        wrapper.eq(Product::getInventory, dto.getInventory(), Utils.isNotNull(dto.getInventory()));
        wrapper.eq(Product::getImage, dto.getImage(), Utils.isNotNull(dto.getImage()));
        wrapper.eq(Product::getImageGallery, dto.getImageGallery(), Utils.isNotNull(dto.getImageGallery()));
        wrapper.eq(Product::getExpertUserId, dto.getExpertUserId(), Utils.isNotNull(dto.getExpertUserId()));
        wrapper.like(Product::getExpertName, dto.getExpertName(), Utils.isNotNull(dto.getExpertName()));
        wrapper.eq(Product::getExpertContact, dto.getExpertContact(), Utils.isNotNull(dto.getExpertContact()));
        wrapper.eq(Product::getExpertUrl, dto.getExpertUrl(), Utils.isNotNull(dto.getExpertUrl()));
        wrapper.eq(Product::getExpertQrCode, dto.getExpertQrCode(), Utils.isNotNull(dto.getExpertQrCode()));
        wrapper.eq(Product::getServiceTime, dto.getServiceTime(), Utils.isNotNull(dto.getServiceTime()));
        wrapper.eq(Product::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        wrapper.orderBy(Product::getSort).desc();
        return wrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProductCreateDTO dto) {
        Product product = BeanCopyUtils.copy(dto, Product.class);
        save(product);
        metaTagService.bindTags(product.getId(), dto.getTagIds(), WingmanTagBind.Product);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProductUpdateDTO dto) {
        Product product = BeanCopyUtils.copy(dto, Product.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(Product::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(product);
        metaTagService.unBindAll(product.getId(), WingmanTagBind.Product);
        metaTagService.bindTags(product.getId(), dto.getTagIds(), WingmanTagBind.Product);
    }

    @Override
    public PageResult<ProductVO> page(ProductListDTO dto) {
        List<Long> productIds = metaTagService.listBusinessIdByTagIds(dto.getTagIds(), WingmanTagBind.Product);
        QueryWrapper queryWrapper = buildQueryWrapper(dto);
        queryWrapper.in(Product::getId, productIds, CollectionUtils.isNotEmpty(productIds));
        Page<ProductVO> page = pageAs(PageUtils.getPage(dto), queryWrapper, ProductVO.class);

        List<ProductVO> productVOList = page.getRecords();
        productVOList.forEach(item -> {
            item.setTagList(List.of());
        });
        page.setRecords(productVOList);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<ProductVO> list(ProductListDTO dto) {
        return listAs(buildQueryWrapper(dto), ProductVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public ProductVO detail(Long id) {
        Product product = getById(id);
        CommonResponseEnum.INVALID_ID.assertNull(product);
        List<MetaTag> metaTags = metaTagService.listByBusinessId(id, WingmanTagBind.Product);
        ProductVO productVO = BeanCopyUtils.copy(product, ProductVO.class);
        productVO.setTagList(metaTags);
        return productVO;
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<ProductImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), ProductImportDTO.class, true);
        List<ProductImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(ProductListDTO dto, HttpServletResponse response) {
        List<ProductVO> list = list(dto);
        String fileName = "商品模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "商品", ProductVO.class, os);
    }
}