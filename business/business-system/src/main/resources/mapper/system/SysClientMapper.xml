<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.admin.mapper.SysClientMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.admin.pojo.po.SysClient">
        <id column="client_id" property="clientId"/>
        <result column="client_key" property="clientKey"/>
        <result column="client_secret" property="clientSecret"/>
        <result column="grant_type_cd" property="grantTypeCd"/>
        <result column="device_type_cd" property="deviceTypeCd"/>
        <result column="active_timeout" property="activeTimeout"/>
        <result column="timeout" property="timeout"/>
        <result column="client_status_cd" property="clientStatusCd"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_id" property="createId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
        <result column="is_lock" property="isLock"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        client_id, client_key, client_secret, grant_type_cd, device_type_cd, active_timeout, timeout, client_status_cd, del_flag, create_dept, create_id, create_time, update_id, update_time, remark, is_lock
    </sql>

</mapper>
