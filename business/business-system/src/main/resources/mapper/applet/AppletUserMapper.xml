<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.applet.mapper.AppletUserMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.applet.pojo.po.AppletUser">
        <id column="id" property="id"/>
        <result column="sys_user_id" property="sysUserId"/>
        <result column="openid" property="openid"/>
        <result column="unionid" property="unionid"/>
        <result column="nickname" property="nickname"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="address" property="address"/>
        <result column="avatar" property="avatar"/>
        <result column="subscribe" property="subscribe"/>
        <result column="enable" property="enable"/>
        <result column="sex" property="sex"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sys_user_id, openid, unionid, nickname, name, phone, address, avatar, subscribe, enable, sex, del_flag, create_time, update_time
    </sql>

</mapper>
