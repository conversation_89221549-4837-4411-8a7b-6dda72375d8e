<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.rescue.work.mapper.WorkAskApplyMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.rescue.work.pojo.po.WorkAskApply">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_id" property="deptId"/>
        <result column="review_user_id" property="reviewUserId"/>
        <result column="ask_content" property="askContent"/>
        <result column="ask_phone" property="askPhone"/>
        <result column="ask_name" property="askName"/>
        <result column="address" property="address"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, dept_id, review_user_id, ask_content, ask_phone, ask_name, address, detail_address, longitude, latitude, remark, status, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
