<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.rescue.work.mapper.WorkEventMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.rescue.work.pojo.po.WorkEvent">
        <id column="id" property="id"/>
        <result column="work_id" property="workId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_id" property="deptId"/>
        <result column="user_id" property="userId"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="image" property="image"/>
        <result column="event_time" property="eventTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, work_id, tenant_id, dept_id, user_id, title, content, image, event_time, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
