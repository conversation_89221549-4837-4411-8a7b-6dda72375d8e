<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.rescue.product.mapper.ProductMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.rescue.product.pojo.po.Product">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_id" property="deptId"/>
        <result column="category_id" property="categoryId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="name" property="name"/>
        <result column="image" property="image"/>
        <result column="product_number" property="productNumber"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, dept_id, category_id, warehouse_id, name, image, product_number, enable, create_time, update_time
    </sql>

</mapper>
