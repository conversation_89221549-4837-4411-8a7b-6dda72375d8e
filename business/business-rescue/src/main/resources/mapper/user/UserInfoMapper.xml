<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.rescue.user.mapper.UserInfoMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.rescue.user.pojo.po.UserInfo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="dept_id" property="deptId"/>
        <result column="name" property="name"/>
        <result column="nick_name" property="nickName"/>
        <result column="number_prefix" property="numberPrefix"/>
        <result column="number" property="number"/>
        <result column="post" property="post"/>
        <result column="avatar" property="avatar"/>
        <result column="phone" property="phone"/>
        <result column="identity_card" property="identityCard"/>
        <result column="identity_start_date" property="identityStartDate"/>
        <result column="identity_end_date" property="identityEndDate"/>
        <result column="passport_number" property="passportNumber"/>
        <result column="insurance_status" property="insuranceStatus"/>
        <result column="politics_status" property="politicsStatus"/>
        <result column="blood_type" property="bloodType"/>
        <result column="sex" property="sex"/>
        <result column="birthday" property="birthday"/>
        <result column="remark" property="remark"/>
        <result column="signature_image" property="signatureImage"/>
        <result column="identity_image" property="identityImage"/>
        <result column="information_image" property="informationImage"/>
        <result column="total_duty_duration" property="totalDutyDuration"/>
        <result column="year_duty_duration" property="yearDutyDuration"/>
        <result column="emergency_contact" property="emergencyContact"/>
        <result column="emergency_contact_phone" property="emergencyContactPhone"/>
        <result column="medical_history" property="medicalHistory"/>
        <result column="allergies_history" property="allergiesHistory"/>
        <result column="enable" property="enable"/>
        <result column="approve_time" property="approveTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, dept_id, name, nick_name, number_prefix, number, post, avatar, phone, identity_card, identity_start_date, identity_end_date, passport_number, insurance_status, politics_status, blood_type, sex, birthday, remark, signature_image, identity_image, information_image, total_duty_duration, year_duty_duration, emergency_contact, emergency_contact_phone, medical_history, allergies_history, enable, approve_time, create_time, update_time, del_flag, create_id, update_id, tenant_id
    </sql>

</mapper>
