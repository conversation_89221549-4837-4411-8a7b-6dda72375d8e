package com.glowxq.rescue.meta.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaWarehouseCreateDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaWarehouseListDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaWarehouseUpdateDTO;
import com.glowxq.rescue.meta.pojo.po.MetaWarehouse;
import com.glowxq.rescue.meta.pojo.vo.MetaWarehouseVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 仓库 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface MetaWarehouseService extends IService<MetaWarehouse> {

    void create(MetaWarehouseCreateDTO dto);

    void update(MetaWarehouseUpdateDTO dto);

    PageResult<MetaWarehouseVO> page(MetaWarehouseListDTO dto);

    List<MetaWarehouseVO> list(MetaWarehouseListDTO dto);

    void remove(SelectIdsDTO dto);

    MetaWarehouseVO detail(Long id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(MetaWarehouseListDTO dto, HttpServletResponse response);
}