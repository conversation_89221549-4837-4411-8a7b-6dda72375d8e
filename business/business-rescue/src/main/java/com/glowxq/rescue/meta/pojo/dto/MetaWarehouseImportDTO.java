package com.glowxq.rescue.meta.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.meta.pojo.po.MetaWarehouse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * MetaWarehouse导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "MetaWarehouse导入DTO")
public class MetaWarehouseImportDTO implements BaseDTO {

    @ExcelProperty(value = "租户id")
    @Schema(description = "租户id")
    private String tenantId;

    @ExcelProperty(value = "部门id")
    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "地址id")
    private Long regionId;

    @ExcelProperty(value = "公共")
    @Schema(description = "公共")
    private Boolean common;

    @ExcelProperty(value = "仓库名")
    @Schema(description = "仓库名")
    private String name;

    @ExcelProperty(value = "仓库图片")
    @Schema(description = "仓库图片")
    private String image;

    @ExcelProperty(value = "仓库地址")
    @Schema(description = "仓库地址")
    private String address;

    @ExcelProperty(value = "详细地址")
    @Schema(description = "详细地址")
    private String detailAddress;

    @ExcelProperty(value = "定位信息")
    @Schema(description = "定位信息")
    private String location;

    @ExcelProperty(value = "经度")
    @Schema(description = "经度")
    private String longitude;

    @ExcelProperty(value = "纬度")
    @Schema(description = "纬度")
    private String latitude;

    @ExcelProperty(value = "启用仓库")
    @Schema(description = "启用仓库")
    private Boolean enable;

    @Override
    public MetaWarehouse buildEntity() {
        return BeanCopyUtils.copy(this, MetaWarehouse.class);
    }
}