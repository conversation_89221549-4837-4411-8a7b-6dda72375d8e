后端完成了的申请列表，详情接口，返回我的申请详情，完成我的申请页面开发。
根据markdown文件的内容，并完善逻辑和相关功能，注意样式和交互要美观以Apple为准 
注意，像订单子项，审批意见的接口只有detail详情接口才会返回，
```java
 @Operation(summary = "我的申请查询")
@SaCheckPermission(value = "order.query_table")
@PostMapping("/order/my/apply/list")
public ApiResult<PageResult<OrderVO>> myApplyList(AppOrderListDTO dto) {
    OrderListDTO orderListDTO = new OrderListDTO();
    orderListDTO.setApplyUserId(LoginUtils.getUserId());
    return ApiPageResult.success(orderService.page(orderListDTO));
}

@Operation(summary = "详情")
@SaCheckPermission(value = "order.query_table")
@GetMapping("/order/detail")
public ApiResult<OrderVO> detail(@RequestParam Long id) {
    return ApiResult.success(orderService.detail(id));
}

@Data
@Schema(description = "Order查询DTO")
public class AppOrderListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "任务编号")
    private String workNumber;

    @Schema(description = "订单编号")
    private String orderNumber;

    @Schema(description = "状态")
    @InEnum(enumClass = OrderReviewStatus.class)
    private String status;

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}

@Data
@Schema(description = "Order返回vo")
public class OrderVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "记录ID")
    private Long id;

    @ExcelProperty(value = "租户ID")
    @Schema(description = "租户ID")
    private String tenantId;

    @ExcelProperty(value = "部门id")
    @Schema(description = "部门id")
    private Long deptId;

    @ExcelProperty(value = "任务id")
    @Schema(description = "任务id")
    private Long workId;

    @ExcelProperty(value = "任务编号")
    @Schema(description = "任务编号")
    private String workNumber;

    @ExcelProperty(value = "订单编号")
    @Schema(description = "订单编号")
    private String orderNumber;

    @ExcelProperty(value = "类型")
    @Schema(description = "类型")
    private String type;

    @ExcelProperty(value = "操作原因")
    @Schema(description = "操作原因")
    private String reason;

    @ExcelProperty(value = "申请用户ID")
    @Schema(description = "申请用户ID")
    private Long applyUserId;

    @ExcelProperty(value = "申请用户名")
    @Schema(description = "申请用户名")
    private String applyName;

    @ExcelProperty(value = "申请用户手机号")
    @Schema(description = "申请用户手机号")
    private String applyPhone;

    @ExcelProperty(value = "出入库")
    @Schema(description = "出入库")
    private Boolean operate;

    @ExcelProperty(value = "审批用户ID")
    @Schema(description = "审批用户ID")
    private Long reviewUserId;

    @ExcelProperty(value = "审批人")
    @Schema(description = "审批人")
    private String reviewName;

    @ExcelProperty(value = "审批人电话")
    @Schema(description = "审批人电话")
    private String reviewPhone;

    @ExcelProperty(value = "审批意见")
    @Schema(description = "审批意见")
    private String reviewOpinion;

    @ExcelProperty(value = "总金额")
    @Schema(description = "总金额")
    private BigDecimal totalPrice;

    @ExcelProperty(value = "物资总数量")
    @Schema(description = "物资总数量")
    private Integer totalAmount;

    @ExcelProperty(value = "状态")
    @Schema(description = "状态")
    private String status;

    @ExcelProperty(value = "备注")
    @Schema(description = "备注")
    private String remark;

    @ExcelProperty(value = "子项")
    @Schema(description = "子项")
    @RelationOneToMany(selfField = "id", targetField = "orderId", targetTable = "order_item")
    private List<OrderItem> orderItems;

    @ExcelProperty(value = "审批记录")
    @Schema(description = "审批记录")
    @RelationOneToMany(selfField = "id", targetField = "orderId", targetTable = "order_review")
    private List<OrderReview> orderReviews;
}

@Data
@Table(value = "order_item", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "订单子项")
public class OrderItem implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "详情ID")
    private Long id;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "物资id")
    private Long productId;

    @Schema(description = "物资名")
    private String productName;

    @Schema(description = "物资图片")
    private String image;

    @Schema(description = "规格名")
    private Long skuId;

    @Schema(description = "规格名")
    private String skuName;

    @Schema(description = "物资规格图片")
    private String skuImage;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    public SkuQuantityBO buildSkuQuantityBO(){
        return new SkuQuantityBO(this.skuId, this.quantity);
    }
}

@Data
@Table(value = "order_review", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "物资审批记录")
public class OrderReview implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description ="记录ID")
    private Long id;

    @Schema(description ="租户ID")
    private String tenantId;

    @Schema(description ="部门ID")
    private Long deptId;

    @Schema(description ="物资记录ID")
    private Long orderId;

    @Schema(description ="审批用户ID")
    private Long reviewUserId;

    @Schema(description ="审批人")
    private String reviewName;

    @Schema(description ="审批人电话")
    private String reviewPhone;

    @Schema(description ="原状态")
    private String originStatus;

    @Schema(description ="审批后状态")
    private String targetStatus;

    @Schema(description ="审批意见")
    private String reviewOpinion;

    @Schema(description ="创建时间")
    private LocalDateTime createTime;

    @Schema(description ="更新时间")
    private LocalDateTime updateTime;

}
```