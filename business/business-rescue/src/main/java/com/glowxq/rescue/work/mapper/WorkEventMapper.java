package com.glowxq.rescue.work.mapper;

import com.glowxq.rescue.work.pojo.po.WorkEvent;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

/**
 * <p>
 * 任务事件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface WorkEventMapper extends BaseMapper<WorkEvent> {

    default void deleteByWorkId(Long workId) {
        QueryWrapper qw = new QueryWrapper();
        qw.eq(WorkEvent::getWorkId, workId);
        deleteByQuery(qw);
    }
}