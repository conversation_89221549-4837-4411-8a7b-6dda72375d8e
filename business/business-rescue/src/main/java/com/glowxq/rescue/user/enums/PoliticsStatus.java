package com.glowxq.rescue.user.enums;

import com.glowxq.core.common.enums.base.BaseType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 政治地位
 *
 * <AUTHOR>
 * @date 2025/06/21
 */
@AllArgsConstructor
@Getter
public enum PoliticsStatus implements BaseType {
    // 政治面貌
    Other("Other", "其他"),

    PartyMember("PartyMember", "中共党员"),

    Teenager("Teenager", "共青团员"),

    <PERSON>("Mass", "群众"),

    ;

    /**
     * code
     */
    private final String code;

    /**
     * 最近类型
     */
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static PoliticsStatus matchCode(String code) {
        for (PoliticsStatus status : PoliticsStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
