package com.glowxq.rescue.meta.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.rescue.meta.mapper.MetaWarehouseMapper;
import com.glowxq.rescue.meta.pojo.dto.MetaWarehouseCreateDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaWarehouseImportDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaWarehouseListDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaWarehouseUpdateDTO;
import com.glowxq.rescue.meta.pojo.po.MetaWarehouse;
import com.glowxq.rescue.meta.pojo.vo.MetaWarehouseVO;
import com.glowxq.rescue.meta.service.MetaWarehouseService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 仓库 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
@RequiredArgsConstructor
public class MetaWarehouseServiceImpl extends ServiceImpl<MetaWarehouseMapper, MetaWarehouse> implements MetaWarehouseService {

    @Override
    public void create(MetaWarehouseCreateDTO dto) {
        MetaWarehouse metaWarehouse = BeanCopyUtils.copy(dto, MetaWarehouse.class);
        save(metaWarehouse);
    }

    @Override
    public void update(MetaWarehouseUpdateDTO dto) {
        MetaWarehouse metaWarehouse = BeanCopyUtils.copy(dto, MetaWarehouse.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(MetaWarehouse::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(metaWarehouse);
    }

    @Override
    public PageResult<MetaWarehouseVO> page(MetaWarehouseListDTO dto) {
        Page<MetaWarehouseVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), MetaWarehouseVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<MetaWarehouseVO> list(MetaWarehouseListDTO dto) {
        return listAs(buildQueryWrapper(dto), MetaWarehouseVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public MetaWarehouseVO detail(Long id) {
        MetaWarehouse metaWarehouse = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(metaWarehouse);
        return BeanCopyUtils.copy(metaWarehouse, MetaWarehouseVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<MetaWarehouseImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), MetaWarehouseImportDTO.class, true);
        List<MetaWarehouseImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(MetaWarehouseListDTO dto, HttpServletResponse response) {
        List<MetaWarehouseVO> list = list(dto);
        String fileName = "仓库模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "仓库", MetaWarehouseVO.class, os);
    }

    private static QueryWrapper buildQueryWrapper(MetaWarehouseListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(MetaWarehouse.class);
        wrapper.eq(MetaWarehouse::getTenantId, dto.getTenantId(), Utils.isNotNull(dto.getTenantId()));
        wrapper.eq(MetaWarehouse::getDeptId, dto.getDeptId(), Utils.isNotNull(dto.getDeptId()));
        wrapper.eq(MetaWarehouse::getCommon, dto.getCommon(), Utils.isNotNull(dto.getCommon()));
        wrapper.like(MetaWarehouse::getName, dto.getName(), Utils.isNotNull(dto.getName()));
        wrapper.eq(MetaWarehouse::getImage, dto.getImage(), Utils.isNotNull(dto.getImage()));
        wrapper.eq(MetaWarehouse::getAddress, dto.getAddress(), Utils.isNotNull(dto.getAddress()));
        wrapper.eq(MetaWarehouse::getDetailAddress, dto.getDetailAddress(), Utils.isNotNull(dto.getDetailAddress()));
        wrapper.eq(MetaWarehouse::getLocation, dto.getLocation(), Utils.isNotNull(dto.getLocation()));
        wrapper.eq(MetaWarehouse::getLongitude, dto.getLongitude(), Utils.isNotNull(dto.getLongitude()));
        wrapper.eq(MetaWarehouse::getLatitude, dto.getLatitude(), Utils.isNotNull(dto.getLatitude()));
        wrapper.eq(MetaWarehouse::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        return wrapper;
    }
}