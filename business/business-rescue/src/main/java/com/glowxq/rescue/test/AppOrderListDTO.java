package com.glowxq.rescue.test;

import com.glowxq.core.common.entity.PageQuery;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.common.valid.annotation.InEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.order.enums.OrderReviewStatus;
import com.glowxq.rescue.order.pojo.po.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * Order查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "Order查询DTO")
public class AppOrderListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "任务编号")
    private String workNumber;

    @Schema(description = "订单编号")
    private String orderNumber;

    @Schema(description = "状态")
    @InEnum(enumClass = OrderReviewStatus.class)
    private String status;

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}