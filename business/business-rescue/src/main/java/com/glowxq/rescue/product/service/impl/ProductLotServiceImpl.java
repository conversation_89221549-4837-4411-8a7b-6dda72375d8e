package com.glowxq.rescue.product.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.rescue.product.mapper.ProductLotMapper;
import com.glowxq.rescue.product.mapper.ProductSkuMapper;
import com.glowxq.rescue.product.pojo.dto.*;
import com.glowxq.rescue.product.pojo.po.ProductLot;
import com.glowxq.rescue.product.pojo.po.ProductSku;
import com.glowxq.rescue.product.pojo.vo.ProductLotVO;
import com.glowxq.rescue.product.service.ProductLotService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 物资批次 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
@RequiredArgsConstructor
public class ProductLotServiceImpl extends ServiceImpl<ProductLotMapper, ProductLot> implements ProductLotService {

    private final ProductSkuMapper productSkuMapper;

    private static QueryWrapper buildQueryWrapper(ProductLotListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(ProductLot.class);
        wrapper.eq(ProductLot::getTenantId, dto.getTenantId(), Utils.isNotNull(dto.getTenantId()));
        wrapper.eq(ProductLot::getDeptId, dto.getDeptId(), Utils.isNotNull(dto.getDeptId()));
        wrapper.eq(ProductLot::getProductId, dto.getProductId(), Utils.isNotNull(dto.getProductId()));
        wrapper.eq(ProductLot::getSkuId, dto.getSkuId(), Utils.isNotNull(dto.getSkuId()));
        wrapper.eq(ProductLot::getLotNumber, dto.getLotNumber(), Utils.isNotNull(dto.getLotNumber()));
        wrapper.eq(ProductLot::getTitle, dto.getTitle(), Utils.isNotNull(dto.getTitle()));
        wrapper.eq(ProductLot::getPrice, dto.getPrice(), Utils.isNotNull(dto.getPrice()));
        wrapper.eq(ProductLot::getQuantity, dto.getQuantity(), Utils.isNotNull(dto.getQuantity()));
        wrapper.between(ProductLot::getStorageDate,
                dto.getStorageDateStart(),
                dto.getStorageDateEnd(),
                Utils.isNotNull(dto.getStorageDateStart()) && Utils.isNotNull(dto.getStorageDateEnd()));
        wrapper.between(ProductLot::getProductDate,
                dto.getProductDateStart(),
                dto.getProductDateEnd(),
                Utils.isNotNull(dto.getProductDateStart()) && Utils.isNotNull(dto.getProductDateEnd()));
        wrapper.between(ProductLot::getExpirationTime,
                dto.getExpirationTimeStart(),
                dto.getExpirationTimeEnd(),
                Utils.isNotNull(dto.getExpirationTimeStart()) && Utils.isNotNull(dto.getExpirationTimeEnd()));
        wrapper.between(ProductLot::getDiscardDate,
                dto.getDiscardDateStart(),
                dto.getDiscardDateEnd(),
                Utils.isNotNull(dto.getDiscardDateStart()) && Utils.isNotNull(dto.getDiscardDateEnd()));
        wrapper.between(ProductLot::getOverhaulLastDate,
                dto.getOverhaulLastDateStart(),
                dto.getOverhaulLastDateEnd(),
                Utils.isNotNull(dto.getOverhaulLastDateStart()) && Utils.isNotNull(dto.getOverhaulLastDateEnd()));
        wrapper.between(ProductLot::getOverhaulNextDate,
                dto.getOverhaulNextDateStart(),
                dto.getOverhaulNextDateEnd(),
                Utils.isNotNull(dto.getOverhaulNextDateStart()) && Utils.isNotNull(dto.getOverhaulNextDateEnd()));
        wrapper.eq(ProductLot::getOverhaulGap, dto.getOverhaulGap(), Utils.isNotNull(dto.getOverhaulGap()));
        return wrapper;
    }

    @Override
    public void create(ProductLotCreateDTO dto) {
        ProductLot productLot = dto.buildEntity();
        if (dto.getSkuId() != null) {
            ProductSku productSku = productSkuMapper.selectOneById(dto.getSkuId());
            productSku.setProductId(productSku.getProductId());
        }
        save(productLot);
    }

    @Override
    public void update(ProductLotUpdateDTO dto) {
        ProductLot productLot = BeanCopyUtils.copy(dto, ProductLot.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(ProductLot::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);
        if (dto.getSkuId() != null) {
            ProductSku productSku = productSkuMapper.selectOneById(dto.getSkuId());
            productSku.setProductId(productSku.getProductId());
        }
        saveOrUpdate(productLot);
    }

    @Override
    public PageResult<ProductLotVO> page(ProductLotListDTO dto) {
        Page<ProductLotVO> page = mapper.paginateWithRelationsAs(PageUtils.getPage(dto), buildQueryWrapper(dto), ProductLotVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<ProductLotVO> list(ProductLotListDTO dto) {
        return listAs(buildQueryWrapper(dto), ProductLotVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public ProductLotVO detail(Long id) {
        ProductLot productLot = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(productLot);
        return BeanCopyUtils.copy(productLot, ProductLotVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<ProductLotImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), ProductLotImportDTO.class, true);
        List<ProductLotImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(ProductLotListDTO dto, HttpServletResponse response) {
        List<ProductLotVO> list = list(dto);
        String fileName = "物资批次模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "物资批次", ProductLotVO.class, os);
    }

    @Override
    public void deleteByProductId(Long productId) {
        mapper.deleteByProductId(productId);
    }

    @Override
    public void deleteBySkuId(Long skuId) {
        mapper.deleteBySkuId(skuId);
    }

    @Override
    public void overhaul(ProductLotOverhaulDTO dto) {
        List<ProductLot> productLots = mapper.selectListByIds(dto.getLotIds());
        List<ProductLot> productLotOverhauls = productLots.stream().peek(productLot -> {
            LocalDate overhauledNextDate = dto.overhaulNextDate(productLot.getOverhaulGap());
            productLot.setOverhaulNextDate(overhauledNextDate);
        }).toList();
        this.updateBatch(productLotOverhauls);
    }
}