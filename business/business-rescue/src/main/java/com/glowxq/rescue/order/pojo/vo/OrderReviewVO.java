package com.glowxq.rescue.order.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * OrderReview返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "OrderReview返回vo")
public class OrderReviewVO implements BaseVO{

    @ExcelIgnore
    @Schema(description =  "记录ID")
    private Long id;

    @ExcelProperty(value = "租户ID")
    @Schema(description =  "租户ID")
    private String tenantId;

    @ExcelProperty(value = "部门ID")
    @Schema(description =  "部门ID")
    private Long deptId;

    @ExcelProperty(value = "物资记录ID")
    @Schema(description =  "物资记录ID")
    private Long orderId;

    @ExcelProperty(value = "审批用户ID")
    @Schema(description =  "审批用户ID")
    private Long reviewUserId;

    @ExcelProperty(value = "审批人")
    @Schema(description =  "审批人")
    private String reviewName;

    @ExcelProperty(value = "审批人电话")
    @Schema(description =  "审批人电话")
    private String reviewPhone;

    @ExcelProperty(value = "原状态")
    @Schema(description =  "原状态")
    private String originStatus;

    @ExcelProperty(value = "审批后状态")
    @Schema(description =  "审批后状态")
    private String targetStatus;

    @ExcelProperty(value = "审批意见")
    @Schema(description =  "审批意见")
    private String reviewOpinion;

}