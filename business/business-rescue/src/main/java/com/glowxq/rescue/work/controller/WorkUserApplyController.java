package com.glowxq.rescue.work.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.common.dto.ReviewDTO;
import com.glowxq.rescue.work.pojo.dto.WorkUserApplyCreateDTO;
import com.glowxq.rescue.work.pojo.dto.WorkUserApplyListDTO;
import com.glowxq.rescue.work.pojo.dto.WorkUserApplyUpdateDTO;
import com.glowxq.rescue.work.pojo.vo.WorkUserApplyVO;
import com.glowxq.rescue.work.service.WorkUserApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * work/报名申请 Api
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Tag(name = "报名申请")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class WorkUserApplyController extends BaseApi {

    private final WorkUserApplyService workUserApplyService;

    @Operation(summary = "新增报名申请")
    @SaCheckPermission(value = "work.user.apply.create")
    @PostMapping("/work-user-apply/create")
    public ApiResult<Void> create(@RequestBody WorkUserApplyCreateDTO dto) {
        workUserApplyService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "审批报名申请")
    @SaCheckPermission(value = "work.user.apply.update")
    @PutMapping("/work-user-apply/review")
    public ApiResult<Void> review(@RequestBody ReviewDTO reviewDto) {
        workUserApplyService.review(reviewDto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "work.user.apply.update")
    @PutMapping("/work-user-apply/update")
    public ApiResult<Void> update(@RequestBody WorkUserApplyUpdateDTO dto) {
        workUserApplyService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "work.user.apply.remove")
    @DeleteMapping("/work-user-apply/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        workUserApplyService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "work.user.apply.query_table")
    @GetMapping("/work-user-apply/list")
    public ApiResult<PageResult<WorkUserApplyVO>> list(WorkUserApplyListDTO dto) {
        return ApiPageResult.success(workUserApplyService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "work.user.apply.query_table")
    @GetMapping("/work-user-apply/detail")
    public ApiResult<WorkUserApplyVO> detail(@RequestParam Long id) {
        return ApiResult.success(workUserApplyService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "work.user.apply.import")
    @PostMapping("/work-user-apply/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        workUserApplyService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "work.user.apply.export")
    @PostMapping("/work-user-apply/export")
    public void exportExcel(@RequestBody WorkUserApplyListDTO dto, HttpServletResponse response) {
        workUserApplyService.exportExcel(dto, response);
    }
}