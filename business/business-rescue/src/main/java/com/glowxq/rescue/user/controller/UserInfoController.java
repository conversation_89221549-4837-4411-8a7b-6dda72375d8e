package com.glowxq.rescue.user.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.constant.GlobalConstant;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.user.pojo.dto.UserInfoCreateDTO;
import com.glowxq.rescue.user.pojo.dto.UserInfoListDTO;
import com.glowxq.rescue.user.pojo.dto.UserInfoRegisterDTO;
import com.glowxq.rescue.user.pojo.dto.UserInfoUpdateDTO;
import com.glowxq.rescue.user.pojo.vo.UserInfoVO;
import com.glowxq.rescue.user.service.UserInfoService;
import com.glowxq.security.pojo.LoginInfo;
import com.glowxq.security.pojo.LoginVO;
import com.glowxq.security.service.AuthService;
import com.glowxq.system.admin.pojo.dto.RegisterDTO;
import com.glowxq.system.admin.pojo.dto.sysuser.UserDeptDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * user/用户信息 Api
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Tag(name = "用户信息")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class UserInfoController extends BaseApi {

    private final UserInfoService userInfoService;

    private final AuthService authService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "user.info.create")
    @PostMapping("/user-info/create")
    public ApiResult<Void> create(@RequestBody UserInfoCreateDTO dto) {
        userInfoService.create(dto);
        return ApiResult.success();
    }

    /**
     * 注册接口
     */
    @SaIgnore
    @PostMapping("/user-info/register")
    public ApiResult<Void> register(@RequestBody UserInfoRegisterDTO dto) {
        userInfoService.register(dto);
        return ApiResult.success();
    }

    /**
     * 微信一键登陆接口
     */
    @SaIgnore
    @PostMapping("/user-info/wechatLogin")
    public ApiResult<LoginVO> wechatLogin(@RequestBody LoginInfo info) {
        return ApiResult.success(authService.loginClient(info));
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "user.info.update")
    @PutMapping("/user-info/update")
    public ApiResult<Void> update(@RequestBody UserInfoUpdateDTO dto) {
        userInfoService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "绑定（批量）用户和部门")
    @SaCheckPermission(value = "sys.user.dept_set_btn", orRole = GlobalConstant.SUPER_ROLE)
    @PostMapping("/user-info/bind")
    public ApiResult<Void> bindDept(@RequestBody UserDeptDTO dto) {
        userInfoService.bindUserDept(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "user.info.remove")
    @DeleteMapping("/user-info/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        userInfoService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "user.info.query_table")
    @GetMapping("/user-info/list")
    public ApiResult<PageResult<UserInfoVO>> list(UserInfoListDTO dto) {
        return ApiPageResult.success(userInfoService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "user.info.query_table")
    @GetMapping("/user-info/detail")
    public ApiResult<UserInfoVO> detail(@RequestParam Long id) {
        return ApiResult.success(userInfoService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "user.info.import")
    @PostMapping("/user-info/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        userInfoService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "user.info.export")
    @PostMapping("/user-info/export")
    public void exportExcel(@RequestBody UserInfoListDTO dto, HttpServletResponse response) {
        userInfoService.exportExcel(dto, response);
    }
}