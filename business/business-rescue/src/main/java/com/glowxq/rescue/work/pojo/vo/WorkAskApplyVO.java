package com.glowxq.rescue.work.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * WorkAskApply返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkAskApply返回vo")
public class WorkAskApplyVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "")
    private Long id;

    @ExcelProperty(value = "租户id")
    @Schema(description = "租户id")
    private String tenantId;

    @ExcelProperty(value = "部门ID")
    @Schema(description = "部门ID")
    private Long deptId;

    @ExcelProperty(value = "审批人id")
    @Schema(description = "审批人id")
    private Long reviewUserId;

    @ExcelProperty(value = "求助详情")
    @Schema(description = "求助详情")
    private String askContent;

    @ExcelProperty(value = "求助人联系方式")
    @Schema(description = "求助人联系方式")
    private String askPhone;

    @ExcelProperty(value = "求助人姓名")
    @Schema(description = "求助人姓名")
    private String askName;

    @ExcelProperty(value = "省市区地址")
    @Schema(description = "省市区地址")
    private String address;

    @ExcelProperty(value = "详细地址")
    @Schema(description = "详细地址")
    private String detailAddress;

    @ExcelProperty(value = "经度")
    @Schema(description = "经度")
    private Integer longitude;

    @ExcelProperty(value = "纬度")
    @Schema(description = "纬度")
    private Integer latitude;

    @ExcelProperty(value = "备注")
    @Schema(description = "备注")
    private String remark;

    @ExcelProperty(value = "状态")
    @Schema(description = "状态")
    private String status;

    @ExcelProperty(value = "申请时间")
    @Schema(description = "申请时间")
    private LocalDateTime applyTime;
}