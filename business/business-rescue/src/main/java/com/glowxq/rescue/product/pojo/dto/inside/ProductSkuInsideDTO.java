package com.glowxq.rescue.product.pojo.dto.inside;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.product.pojo.po.ProductSku;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * ProductSku添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "ProductSku添加DTO")
public class ProductSkuInsideDTO implements BaseDTO {

    @Schema(description = "规格名称")
    private String name;

    @Schema(description = "规格图片")
    private String image;

    @Schema(description = "库存")
    private Integer stock;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "启用")
    private Boolean enable;

    /**
     * 物资批次
     */
    private List<ProductLotInsideDTO> productLots;

    @Override
    public ProductSku buildEntity() {
        return BeanCopyUtils.copy(this, ProductSku.class);
    }
}