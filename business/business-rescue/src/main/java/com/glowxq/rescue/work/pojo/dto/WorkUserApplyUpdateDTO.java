package com.glowxq.rescue.work.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.pojo.po.WorkUserApply;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * WorkUserApply修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkUserApply修改DTO")
public class WorkUserApplyUpdateDTO implements BaseDTO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "任务ID")
    private Long workId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "审批状态")
    private String status;

    @Schema(description = "姓名")
    private String name;

    @Override
    public WorkUserApply buildEntity() {
        return BeanCopyUtils.copy(this, WorkUserApply.class);
    }
}