package com.glowxq.rescue.common.enums;

import com.glowxq.core.common.enums.base.BaseType;
import com.glowxq.core.common.exception.common.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物资订单审批状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20
 */
@AllArgsConstructor
@Getter
public enum ReviewStatus implements BaseType {
    None("None", 0, "等待审批"),
    // 审批通过
    Approved("Approved", 2, "审批通过"),
    // 审批拒绝
    Rejected("Rejected", 3, "审批拒绝"),
    ;

    /**
     * code
     */
    private final String code;

    /**
     * 索引
     */
    private final Integer index;

    /**
     * 最近类型
     */
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static ReviewStatus matchCode(String code) {
        for (ReviewStatus orderReviewStatus : ReviewStatus.values()) {
            if (orderReviewStatus.getCode().equals(code)) {
                return orderReviewStatus;
            }
        }
        return null;
    }

    /**
     * 根据index获取枚举
     *
     * @param index
     * @return
     */
    public static ReviewStatus matchIndex(Integer index) {
        if (index == null || index < 0 || index > ReviewStatus.Rejected.getIndex()) {
            throw new BusinessException("无效的[OrderReviewStatus]状态索引 index:" + index);
        }
        for (ReviewStatus orderReviewStatus : ReviewStatus.values()) {
            if (orderReviewStatus.getIndex().equals(index)) {
                return orderReviewStatus;
            }
        }
        throw new BusinessException("无效的[OrderReviewStatus]状态索引 index:" + index);
    }
}
