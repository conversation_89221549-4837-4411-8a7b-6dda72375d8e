package com.glowxq.rescue.work.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.rescue.work.pojo.dto.WorkSignCreateDTO;
import com.glowxq.rescue.work.pojo.dto.WorkSignDTO;
import com.glowxq.rescue.work.pojo.dto.WorkSignListDTO;
import com.glowxq.rescue.work.pojo.dto.WorkSignUpdateDTO;
import com.glowxq.rescue.work.pojo.po.WorkSign;
import com.glowxq.rescue.work.pojo.vo.WorkSignVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 任务签到 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface WorkSignService extends IService<WorkSign> {

    void create(WorkSignCreateDTO dto);

    void update(WorkSignUpdateDTO dto);

    PageResult<WorkSignVO> page(WorkSignListDTO dto);

    List<WorkSignVO> list(WorkSignListDTO dto);

    void remove(SelectIdsDTO dto);

    WorkSignVO detail(Long id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(WorkSignListDTO dto, HttpServletResponse response);

    void sign(WorkSignDTO dto);
}