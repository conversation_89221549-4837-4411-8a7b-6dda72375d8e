package com.glowxq.rescue.work.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.common.dto.AskReviewDTO;
import com.glowxq.rescue.work.pojo.dto.WorkAskApplyCreateDTO;
import com.glowxq.rescue.work.pojo.dto.WorkAskApplyListDTO;
import com.glowxq.rescue.work.pojo.dto.WorkAskApplyUpdateDTO;
import com.glowxq.rescue.work.pojo.vo.WorkAskApplyVO;
import com.glowxq.rescue.work.service.WorkAskApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * work/求助信息 Api
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Tag(name = "求助信息")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class WorkAskApplyController extends BaseApi {

    private final WorkAskApplyService workAskApplyService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "work.ask.apply.create")
    @PostMapping("/work-ask-apply/create")
    public ApiResult<Void> create(@RequestBody WorkAskApplyCreateDTO dto) {
        workAskApplyService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "审批")
    @SaCheckPermission(value = "work.ask.apply.update")
    @PutMapping("/work-ask-apply/review")
    public ApiResult<Void> review(@RequestBody AskReviewDTO reviewDto) {
        workAskApplyService.review(reviewDto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "work.ask.apply.update")
    @PutMapping("/work-ask-apply/update")
    public ApiResult<Void> update(@RequestBody WorkAskApplyUpdateDTO dto) {
        workAskApplyService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "work.ask.apply.remove")
    @DeleteMapping("/work-ask-apply/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        workAskApplyService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "work.ask.apply.query_table")
    @GetMapping("/work-ask-apply/list")
    public ApiResult<PageResult<WorkAskApplyVO>> list(WorkAskApplyListDTO dto) {
        return ApiPageResult.success(workAskApplyService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "work.ask.apply.query_table")
    @GetMapping("/work-ask-apply/detail")
    public ApiResult<WorkAskApplyVO> detail(@RequestParam Long id) {
        return ApiResult.success(workAskApplyService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "work.ask.apply.import")
    @PostMapping("/work-ask-apply/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        workAskApplyService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "work.ask.apply.export")
    @PostMapping("/work-ask-apply/export")
    public void exportExcel(@RequestBody WorkAskApplyListDTO dto, HttpServletResponse response) {
        workAskApplyService.exportExcel(dto, response);
    }
}