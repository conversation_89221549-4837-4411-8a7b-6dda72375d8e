package com.glowxq.rescue.work.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * WorkSign返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkSign返回vo")
public class WorkSignVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "id")
    private Long id;

    @ExcelProperty(value = "租户ID")
    @Schema(description = "租户ID")
    private String tenantId;

    @ExcelProperty(value = "部门ID")
    @Schema(description = "部门ID")
    private Long deptId;

    @ExcelProperty(value = "任务ID")
    @Schema(description = "任务ID")
    private Long workId;

    @ExcelProperty(value = "用户ID")
    @Schema(description = "用户ID")
    private Long userId;

    @ExcelProperty(value = "签到/签退")
    @Schema(description = "签到/签退")
    private String signType;

    @ExcelProperty(value = "姓名")
    @Schema(description = "姓名")
    private String name;

    @ExcelProperty(value = "签到日期")
    @Schema(description = "签到日期")
    private LocalDate signDate;

    @ExcelProperty(value = "签到时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "签到时间")
    private LocalDateTime signTime;
}