package com.glowxq.rescue.work.mapper;

import com.glowxq.rescue.work.pojo.po.WorkUser;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

/**
 * <p>
 * 任务参与人员 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface WorkUserMapper extends BaseMapper<WorkUser> {

    default void deleteByWorkId(Long workId) {
        QueryWrapper qw = new QueryWrapper();
        qw.eq(WorkUser::getWorkId, workId);
        deleteByQuery(qw);
    }
}