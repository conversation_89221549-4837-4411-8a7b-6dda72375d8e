package com.glowxq.rescue.meta.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.meta.pojo.dto.MetaSkillCreateDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaSkillListDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaSkillUpdateDTO;
import com.glowxq.rescue.meta.pojo.vo.MetaSkillVO;
import com.glowxq.rescue.meta.service.MetaSkillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * meta/技能数据 Api
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Tag(name = "技能数据")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class MetaSkillController extends BaseApi {

    private final MetaSkillService metaSkillService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "meta.skill.create")
    @PostMapping("/meta-skill/create")
    public ApiResult<Void> create(@RequestBody MetaSkillCreateDTO dto) {
        metaSkillService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "meta.skill.update")
    @PutMapping("/meta-skill/update")
    public ApiResult<Void> update(@RequestBody MetaSkillUpdateDTO dto) {
        metaSkillService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "meta.skill.remove")
    @DeleteMapping("/meta-skill/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        metaSkillService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "meta.skill.query_table")
    @GetMapping("/meta-skill/list")
    public ApiResult<PageResult<MetaSkillVO>> list(MetaSkillListDTO dto) {
        return ApiPageResult.success(metaSkillService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "meta.skill.query_table")
    @GetMapping("/meta-skill/detail")
    public ApiResult<MetaSkillVO> detail(@RequestParam Long id) {
        return ApiResult.success(metaSkillService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "meta.skill.import")
    @PostMapping("/meta-skill/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        metaSkillService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "meta.skill.export")
    @PostMapping("/meta-skill/export")
    public void exportExcel(@RequestBody MetaSkillListDTO dto, HttpServletResponse response) {
        metaSkillService.exportExcel(dto, response);
    }
}