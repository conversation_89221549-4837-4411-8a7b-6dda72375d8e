package com.glowxq.rescue.product.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.product.pojo.dto.ProductSkuCreateDTO;
import com.glowxq.rescue.product.pojo.dto.ProductSkuListDTO;
import com.glowxq.rescue.product.pojo.dto.ProductSkuUpdateDTO;
import com.glowxq.rescue.product.pojo.vo.ProductSkuVO;
import com.glowxq.rescue.product.service.ProductSkuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * product/物资规格 Api
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Tag(name = "物资规格")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class ProductSkuController extends BaseApi {

    private final ProductSkuService productSkuService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "product.sku.create")
    @PostMapping("/product-sku/create")
    public ApiResult<Void> create(@RequestBody ProductSkuCreateDTO dto) {
        productSkuService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "product.sku.update")
    @PutMapping("/product-sku/update")
    public ApiResult<Void> update(@RequestBody ProductSkuUpdateDTO dto) {
        productSkuService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "product.sku.remove")
    @DeleteMapping("/product-sku/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        productSkuService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "product.sku.query_table")
    @GetMapping("/product-sku/list")
    public ApiResult<PageResult<ProductSkuVO>> list(ProductSkuListDTO dto) {
        return ApiPageResult.success(productSkuService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "product.sku.query_table")
    @GetMapping("/product-sku/detail")
    public ApiResult<ProductSkuVO> detail(@RequestParam Long id) {
        return ApiResult.success(productSkuService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "product.sku.import")
    @PostMapping("/product-sku/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        productSkuService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "product.sku.export")
    @PostMapping("/product-sku/export")
    public void exportExcel(@RequestBody ProductSkuListDTO dto, HttpServletResponse response) {
        productSkuService.exportExcel(dto, response);
    }
}