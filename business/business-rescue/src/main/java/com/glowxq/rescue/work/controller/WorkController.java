package com.glowxq.rescue.work.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.work.pojo.dto.WorkCreateDTO;
import com.glowxq.rescue.work.pojo.dto.WorkListDTO;
import com.glowxq.rescue.work.pojo.dto.WorkUpdateDTO;
import com.glowxq.rescue.work.pojo.vo.WorkVO;
import com.glowxq.rescue.work.service.WorkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * work/任务 Api
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Tag(name = "任务")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class WorkController extends BaseApi {

    private final WorkService workService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "work.create")
    @PostMapping("/work/create")
    public ApiResult<Void> create(@RequestBody WorkCreateDTO dto) {
        workService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "work.update")
    @PutMapping("/work/update")
    public ApiResult<Void> update(@RequestBody WorkUpdateDTO dto) {
        workService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "work.remove")
    @DeleteMapping("/work/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        workService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "work.query_table")
    @GetMapping("/work/list")
    public ApiResult<PageResult<WorkVO>> list(WorkListDTO dto) {
        return ApiPageResult.success(workService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "work.query_table")
    @GetMapping("/work/detail")
    public ApiResult<WorkVO> detail(@RequestParam Long id) {
        return ApiResult.success(workService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "work.import")
    @PostMapping("/work/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        workService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "work.export")
    @PostMapping("/work/export")
    public void exportExcel(@RequestBody WorkListDTO dto, HttpServletResponse response) {
        workService.exportExcel(dto, response);
    }
}