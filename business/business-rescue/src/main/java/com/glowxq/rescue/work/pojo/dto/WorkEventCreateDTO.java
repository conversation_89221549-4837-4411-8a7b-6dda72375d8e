package com.glowxq.rescue.work.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.pojo.po.WorkEvent;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <p>
 * WorkEvent添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkEvent添加DTO")
public class WorkEventCreateDTO implements BaseDTO {

    @Schema(description = "任务ID")
    private Long workId;

    @Schema(description = "事件标题")
    private String title;

    @Schema(description = "事件内容")
    private String content;

    @Schema(description = "事件图片")
    private String image;

    @Schema(description = "事件时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;

    @Override
    public WorkEvent buildEntity() {
        return BeanCopyUtils.copy(this, WorkEvent.class);
    }
}