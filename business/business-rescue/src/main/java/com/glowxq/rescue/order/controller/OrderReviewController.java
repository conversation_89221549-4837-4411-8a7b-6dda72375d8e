package com.glowxq.rescue.order.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.order.pojo.dto.OrderReviewCreateDTO;
import com.glowxq.rescue.order.pojo.dto.OrderReviewListDTO;
import com.glowxq.rescue.order.pojo.dto.OrderReviewUpdateDTO;
import com.glowxq.rescue.order.pojo.vo.OrderReviewVO;
import com.glowxq.rescue.order.service.OrderReviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * order/物资审批记录 Api
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Tag(name =  "物资审批记录")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class OrderReviewController extends BaseApi  {

    private final OrderReviewService orderReviewService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "order.review.create")
    @PostMapping("/order-review/create")
    public ApiResult<Void> create(@RequestBody OrderReviewCreateDTO dto) {
        orderReviewService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "order.review.update")
    @PutMapping("/order-review/update")
    public ApiResult<Void> update(@RequestBody OrderReviewUpdateDTO dto) {
        orderReviewService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "order.review.remove")
    @DeleteMapping("/order-review/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        orderReviewService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "order.review.query_table")
    @GetMapping("/order-review/list")
    public ApiResult<PageResult<OrderReviewVO>> list(OrderReviewListDTO dto) {
        return ApiPageResult.success(orderReviewService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "order.review.query_table")
    @GetMapping("/order-review/detail")
    public ApiResult<OrderReviewVO> detail(@RequestParam Long id) {
        return ApiResult.success(orderReviewService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
      @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "order.review.import")
    @PostMapping("/order-review/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        orderReviewService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "order.review.export")
    @PostMapping("/order-review/export")
    public void exportExcel(@RequestBody OrderReviewListDTO dto, HttpServletResponse response) {
        orderReviewService.exportExcel(dto, response);
    }
}