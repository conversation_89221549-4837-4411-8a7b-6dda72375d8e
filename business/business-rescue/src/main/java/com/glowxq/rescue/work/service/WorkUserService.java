package com.glowxq.rescue.work.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.rescue.user.pojo.po.UserInfo;
import com.glowxq.rescue.work.pojo.dto.WorkUserCreateDTO;
import com.glowxq.rescue.work.pojo.dto.WorkUserListDTO;
import com.glowxq.rescue.work.pojo.dto.WorkUserUpdateDTO;
import com.glowxq.rescue.work.pojo.po.WorkUser;
import com.glowxq.rescue.work.pojo.vo.WorkUserVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 任务参与人员 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface WorkUserService extends IService<WorkUser> {

    void create(WorkUserCreateDTO dto);

    void update(WorkUserUpdateDTO dto);

    PageResult<WorkUserVO> page(WorkUserListDTO dto);

    List<WorkUserVO> list(WorkUserListDTO dto);

    void remove(SelectIdsDTO dto);

    WorkUserVO detail(Long id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(WorkUserListDTO dto, HttpServletResponse response);

    void bindUsers(Long id, List<UserInfo> userInfos);

    void deleteByWorkId(Long id);
}