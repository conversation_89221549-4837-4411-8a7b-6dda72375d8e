package com.glowxq.rescue.meta.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.meta.pojo.po.MetaSkill;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * MetaSkill导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "MetaSkill导入DTO")
public class MetaSkillImportDTO implements BaseDTO {

    @ExcelProperty(value = "租户编号")
    @Schema(description = "租户编号")
    private String tenantId;

    @ExcelProperty(value = "部门ID")
    @Schema(description = "部门ID")
    private Long deptId;

    @ExcelProperty(value = "父id")
    @Schema(description = "父id")
    private Long parentId;

    @ExcelProperty(value = "祖级列表")
    @Schema(description = "祖级列表")
    private String ancestors;

    @ExcelProperty(value = "技能名称")
    @Schema(description = "技能名称")
    private String name;

    @ExcelProperty(value = "显示顺序")
    @Schema(description = "显示顺序")
    private Integer sort;

    @ExcelProperty(value = "图片")
    @Schema(description = "图片")
    private String image;

    @ExcelProperty(value = "启用")
    @Schema(description = "启用")
    private Boolean enable;

    @Override
    public MetaSkill buildEntity() {
        return BeanCopyUtils.copy(this, MetaSkill.class);
    }
}