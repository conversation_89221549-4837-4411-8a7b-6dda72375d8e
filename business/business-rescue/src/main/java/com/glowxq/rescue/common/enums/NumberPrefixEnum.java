package com.glowxq.rescue.common.enums;

import com.glowxq.core.common.enums.base.BaseType;
import com.glowxq.core.common.enums.base.NumberGen;
import lombok.Getter;

/**
 * ErrorPrefixEnum - 定义错误前缀的枚举类。
 * <p>
 * 此枚举类用于集中管理和定义应用中的错误前缀，以便统一错误代码格式。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-11
 */
@Getter
public enum NumberPrefixEnum implements BaseType, NumberGen {

    Product("PP", "物资"),
    ProductSku("PS", "物资规格"),
    ProductLot("PL", "物资批次"),
    Order("OO", "订单"),
    Work("WW", "任务"),
    ;

    /**
     * 前缀标识
     */
    private final String code;

    /**
     * 描述
     */
    private final String name;

    NumberPrefixEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String numberPrefix() {
        return code;
    }
}
