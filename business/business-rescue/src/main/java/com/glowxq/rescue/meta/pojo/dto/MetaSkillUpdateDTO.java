package com.glowxq.rescue.meta.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.meta.pojo.po.MetaSkill;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * MetaSkill修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "MetaSkill修改DTO")
public class MetaSkillUpdateDTO implements BaseDTO {

    @Schema(description = "技能id")
    private Long id;

    @Schema(description = "租户编号")
    private String tenantId;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "父id")
    private Long parentId;

    @Schema(description = "祖级列表")
    private String ancestors;

    @Schema(description = "技能名称")
    private String name;

    @Schema(description = "显示顺序")
    private Integer sort;

    @Schema(description = "图片")
    private String image;

    @Schema(description = "启用")
    private Boolean enable;

    @Override
    public MetaSkill buildEntity() {
        return BeanCopyUtils.copy(this, MetaSkill.class);
    }
}