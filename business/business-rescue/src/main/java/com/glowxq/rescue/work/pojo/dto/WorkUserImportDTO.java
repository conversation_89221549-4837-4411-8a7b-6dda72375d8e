package com.glowxq.rescue.work.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.pojo.po.WorkUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * WorkUser导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkUser导入DTO")
public class WorkUserImportDTO implements BaseDTO {

    @ExcelProperty(value = "租户ID")
    @Schema(description = "租户ID")
    private String tenantId;

    @ExcelProperty(value = "部门ID")
    @Schema(description = "部门ID")
    private Long deptId;

    @ExcelProperty(value = "任务ID")
    @Schema(description = "任务ID")
    private Long workId;

    @ExcelProperty(value = "用户ID")
    @Schema(description = "用户ID")
    private Long userId;

    @ExcelProperty(value = "姓名")
    @Schema(description = "姓名")
    private String name;

    @Override
    public WorkUser buildEntity() {
        return BeanCopyUtils.copy(this, WorkUser.class);
    }
}