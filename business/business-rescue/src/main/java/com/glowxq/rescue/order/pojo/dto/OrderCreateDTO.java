package com.glowxq.rescue.order.pojo.dto;

import com.glowxq.core.common.entity.BaseUserInfo;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.common.valid.annotation.InEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.NumberUtils;
import com.glowxq.rescue.common.enums.NumberPrefixEnum;
import com.glowxq.rescue.order.enums.OrderReviewStatus;
import com.glowxq.rescue.order.enums.OrderType;
import com.glowxq.rescue.order.pojo.bo.SkuQuantityBO;
import com.glowxq.rescue.order.pojo.po.Order;
import com.glowxq.security.core.util.LoginUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * Order添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "Order添加DTO")
public class OrderCreateDTO implements BaseDTO {

    @Schema(description = "任务编号")
    private String workNumber;

    @Schema(description = "类型")
    @InEnum(enumClass = OrderType.class)
    private String type;

    @Schema(description = "操作原因")
    private String reason;

    @Schema(description = "备注")
    private String remark;

    @Size(min = 1, max = 100, message = "最多只能选择100个规格")
    @Schema(description = "skuId")
    private List<SkuQuantityBO> skuQuantityList;

    @Override
    public Order buildEntity() {
        Order order = BeanCopyUtils.copy(this, Order.class);
        order.setOrderNumber(NumberUtils.generateNumber(NumberPrefixEnum.Order));
        BaseUserInfo userInfo = LoginUtils.getUserInfo();
        order.setApplyUserId(userInfo.getId());
        order.setApplyName(userInfo.getName());
        order.setApplyPhone(userInfo.getPhone());
        order.setStatus(OrderReviewStatus.None.getCode());
        return order;
    }

    public List<Long> skuIds() {
        return skuQuantityList.stream().map(SkuQuantityBO::getSkuId).toList();
    }
}