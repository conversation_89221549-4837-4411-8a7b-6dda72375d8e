package com.glowxq.rescue.work.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.rescue.common.dto.ReviewDTO;
import com.glowxq.rescue.work.pojo.dto.WorkUserApplyCreateDTO;
import com.glowxq.rescue.work.pojo.dto.WorkUserApplyListDTO;
import com.glowxq.rescue.work.pojo.dto.WorkUserApplyUpdateDTO;
import com.glowxq.rescue.work.pojo.po.WorkUserApply;
import com.glowxq.rescue.work.pojo.vo.WorkUserApplyVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 报名申请 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface WorkUserApplyService extends IService<WorkUserApply> {

    void create(WorkUserApplyCreateDTO dto);

    void update(WorkUserApplyUpdateDTO dto);

    PageResult<WorkUserApplyVO> page(WorkUserApplyListDTO dto);

    List<WorkUserApplyVO> list(WorkUserApplyListDTO dto);

    void remove(SelectIdsDTO dto);

    WorkUserApplyVO detail(Long id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(WorkUserApplyListDTO dto, HttpServletResponse response);

    void review(ReviewDTO reviewDTO);
}