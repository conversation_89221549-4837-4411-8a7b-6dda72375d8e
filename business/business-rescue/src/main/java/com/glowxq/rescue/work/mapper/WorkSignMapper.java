package com.glowxq.rescue.work.mapper;

import com.glowxq.rescue.work.enums.SignType;
import com.glowxq.rescue.work.pojo.po.WorkSign;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

/**
 * <p>
 * 任务签到 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface WorkSignMapper extends BaseMapper<WorkSign> {

    default WorkSign getSign(Long workId, Long userId, SignType signType) {
        QueryWrapper qw = QueryWrapper.create();
        qw.eq(WorkSign::getWorkId, workId)
          .eq(WorkSign::getUserId, userId)
          .eq(WorkSign::getSignType, signType);
        return selectOneByQuery(qw);
    }
}