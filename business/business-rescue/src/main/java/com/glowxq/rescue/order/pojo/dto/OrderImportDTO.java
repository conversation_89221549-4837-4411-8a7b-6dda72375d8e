package com.glowxq.rescue.order.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.order.pojo.po.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * Order导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "Order导入DTO")
public class OrderImportDTO implements BaseDTO {

    @ExcelProperty(value = "租户ID")
    @Schema(description = "租户ID")
    private String tenantId;

    @ExcelProperty(value = "部门id")
    @Schema(description = "部门id")
    private Long deptId;

    @ExcelProperty(value = "任务id")
    @Schema(description = "任务id")
    private Long workId;

    @ExcelProperty(value = "任务编号")
    @Schema(description = "任务编号")
    private String workNumber;

    @ExcelProperty(value = "订单编号")
    @Schema(description = "订单编号")
    private String orderNumber;

    @ExcelProperty(value = "类型")
    @Schema(description = "类型")
    private String type;

    @ExcelProperty(value = "操作原因")
    @Schema(description = "操作原因")
    private String reason;

    @ExcelProperty(value = "申请用户ID")
    @Schema(description = "申请用户ID")
    private Long applyUserId;

    @ExcelProperty(value = "申请用户名")
    @Schema(description = "申请用户名")
    private String applyName;

    @ExcelProperty(value = "申请用户手机号")
    @Schema(description = "申请用户手机号")
    private String applyPhone;

    @ExcelProperty(value = "出入库")
    @Schema(description = "出入库")
    private Boolean operate;

    @ExcelProperty(value = "审批用户ID")
    @Schema(description = "审批用户ID")
    private Long reviewUserId;

    @ExcelProperty(value = "审批人")
    @Schema(description = "审批人")
    private String reviewName;

    @ExcelProperty(value = "审批人电话")
    @Schema(description = "审批人电话")
    private String reviewPhone;

    @ExcelProperty(value = "审批意见")
    @Schema(description = "审批意见")
    private String reviewOpinion;

    @ExcelProperty(value = "总金额")
    @Schema(description = "总金额")
    private BigDecimal totalPrice;

    @ExcelProperty(value = "物资总数量")
    @Schema(description = "物资总数量")
    private Integer totalAmount;

    @ExcelProperty(value = "状态")
    @Schema(description = "状态")
    private String status;

    @ExcelProperty(value = "备注")
    @Schema(description = "备注")
    private String remark;

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}