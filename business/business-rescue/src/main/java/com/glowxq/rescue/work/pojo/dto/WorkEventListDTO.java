package com.glowxq.rescue.work.pojo.dto;

import com.glowxq.core.common.entity.PageQuery;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.pojo.po.WorkEvent;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <p>
 * WorkEvent查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkEvent查询DTO")
public class WorkEventListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "任务ID")
    private Long workId;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "记录人员ID")
    private Long userId;

    @Schema(description = "事件标题")
    private String title;

    @Schema(description = "事件内容")
    private String content;

    @Schema(description = "事件图片")
    private String image;

    @Schema(description = "事件时间开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTimeStart;

    @Schema(description = "事件时间结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTimeEnd;

    @Override
    public WorkEvent buildEntity() {
        return BeanCopyUtils.copy(this, WorkEvent.class);
    }
}