package com.glowxq.rescue.meta.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * <p>
 * 技能数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Table(value = "meta_skill", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "技能数据")
public class MetaSkill implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Schema(description = "技能id")
    private Long id;

    @Schema(description = "租户编号")
    private String tenantId;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "父id")
    private Long parentId;

    @Schema(description = "祖级列表")
    private String ancestors;

    @Schema(description = "技能名称")
    private String name;

    @Schema(description = "显示顺序")
    private Integer sort;

    @Schema(description = "图片")
    private String image;

    @Schema(description = "启用")
    private Boolean enable;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}