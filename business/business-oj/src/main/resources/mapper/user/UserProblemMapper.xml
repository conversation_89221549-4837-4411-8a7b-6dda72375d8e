<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.oj.user.mapper.UserProblemMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.oj.user.pojo.po.UserProblem">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="problem_id" property="problemId"/>
        <result column="problem_key" property="problemKey"/>
        <result column="problem_title" property="problemTitle"/>
        <result column="judge_id" property="judgeId"/>
        <result column="judge_status" property="judgeStatus"/>
        <result column="score" property="score"/>
        <result column="code" property="code"/>
        <result column="options" property="options"/>
        <result column="flow_image" property="flowImage"/>
        <result column="problem_type" property="problemType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, problem_id, problem_key, problem_title, judge_id, judge_status, score, code, options, flow_image, problem_type, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
