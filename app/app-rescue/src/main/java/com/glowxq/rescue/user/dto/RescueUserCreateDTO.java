package com.glowxq.rescue.user.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.system.admin.pojo.dto.sysuser.SysUserCreateDTO;
import com.glowxq.system.admin.pojo.po.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * OJUserCreateDTO
 *
 * <AUTHOR>
 * @since 2023/8/23
 */
@Data
@Schema(description = "SysUser添加DTO")
public class RescueUserCreateDTO extends SysUserCreateDTO implements BaseDTO {

    @Override
    public BaseEntity buildEntity() {
        return BeanCopyUtils.copy(this, SysUser.class);
    }
}
